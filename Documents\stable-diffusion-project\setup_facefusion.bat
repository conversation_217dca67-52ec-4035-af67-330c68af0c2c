@echo off
echo =================================
echo    FaceFusion Setup Script
echo    Industry Leading Faceswap
echo =================================
echo.

REM Create FaceFusion directory
echo Creating FaceFusion directory...
mkdir C:\FaceFusion 2>nul
cd /d C:\FaceFusion

echo.
echo Downloading FaceFusion...
echo This may take a few minutes...
echo.

REM Clone FaceFusion repository
git clone https://github.com/facefusion/facefusion.git .

if exist facefusion.py (
    echo.
    echo ✅ FaceFusion downloaded successfully!
    echo.
    
    echo Installing Python dependencies...
    python install.py
    
    echo.
    echo ✅ FaceFusion setup completed!
    echo.
    echo To run FaceFusion:
    echo   cd C:\FaceFusion
    echo   python facefusion.py run
    echo.
    echo For headless mode:
    echo   python facefusion.py headless-run
    echo.
    
) else (
    echo ❌ Download failed. Please check your internet connection.
    echo.
    echo Manual installation:
    echo 1. Go to https://github.com/facefusion/facefusion
    echo 2. Download ZIP file
    echo 3. Extract to C:\FaceFusion
    echo 4. Run: python install.py
)

echo.
echo Press any key to continue...
pause >nul
