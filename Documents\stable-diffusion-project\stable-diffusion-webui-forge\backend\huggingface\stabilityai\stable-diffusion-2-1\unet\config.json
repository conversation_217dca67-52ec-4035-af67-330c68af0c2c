{"_class_name": "UNet2DConditionModel", "_diffusers_version": "0.10.0.dev0", "act_fn": "silu", "attention_head_dim": [5, 10, 20, 20], "block_out_channels": [320, 640, 1280, 1280], "center_input_sample": false, "cross_attention_dim": 1024, "down_block_types": ["CrossAttnDownBlock2D", "CrossAttnDownBlock2D", "CrossAttnDownBlock2D", "DownBlock2D"], "downsample_padding": 1, "dual_cross_attention": false, "flip_sin_to_cos": true, "freq_shift": 0, "in_channels": 4, "layers_per_block": 2, "mid_block_scale_factor": 1, "norm_eps": 1e-05, "norm_num_groups": 32, "num_class_embeds": null, "only_cross_attention": false, "out_channels": 4, "sample_size": 96, "up_block_types": ["UpBlock2D", "CrossAttnUpBlock2D", "CrossAttnUpBlock2D", "CrossAttnUpBlock2D"], "use_linear_projection": true, "upcast_attention": true}