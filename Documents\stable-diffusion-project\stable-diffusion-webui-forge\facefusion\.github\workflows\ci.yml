name: ci

on: [ push, pull_request ]

jobs:
 lint:
  runs-on: ubuntu-latest
  steps:
  - name: Checkout
    uses: actions/checkout@v4
  - name: Set up Python 3.12
    uses: actions/setup-python@v5
    with:
     python-version: '3.12'
  - run: pip install flake8
  - run: pip install flake8-import-order
  - run: pip install mypy
  - run: flake8 facefusion.py install.py
  - run: flake8 facefusion tests
  - run: mypy facefusion.py install.py
  - run: mypy facefusion tests
 test:
  strategy:
   matrix:
    os: [ macos-latest, ubuntu-latest, windows-latest ]
  runs-on: ${{ matrix.os }}
  steps:
  - name: Checkout
    uses: actions/checkout@v4
  - name: Set up FFmpeg
    uses: AnimMouse/setup-ffmpeg@v1
  - name: Set up Python 3.12
    uses: actions/setup-python@v5
    with:
     python-version: '3.12'
  - run: python install.py --onnxruntime default --skip-conda
  - run: pip install pytest
  - run: pytest
 report:
  needs: test
  runs-on: ubuntu-latest
  steps:
  - name: Checkout
    uses: actions/checkout@v4
  - name: Set up FFmpeg
    uses: FedericoCarboni/setup-ffmpeg@v3
  - name: Set up Python 3.12
    uses: actions/setup-python@v5
    with:
     python-version: '3.12'
  - run: python install.py --onnxruntime default --skip-conda
  - run: pip install coveralls
  - run: pip install pytest
  - run: pip install pytest-cov
  - run: pytest tests --cov facefusion
  - run: coveralls --service github
    env:
     GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
