{"_class_name": "FluxPipeline", "_diffusers_version": "0.30.0.dev0", "scheduler": ["diffusers", "FlowMatchEulerDiscreteScheduler"], "text_encoder": ["transformers", "CLIPTextModel"], "text_encoder_2": ["transformers", "T5EncoderModel"], "tokenizer": ["transformers", "CLIPTokenizer"], "tokenizer_2": ["transformers", "T5TokenizerFast"], "transformer": ["diffusers", "FluxTransformer2DModel"], "vae": ["diffusers", "AutoencoderKL"]}