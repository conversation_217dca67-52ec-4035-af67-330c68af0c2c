{"_class_name": "StableDiffusion3Pipeline", "_diffusers_version": "0.29.0.dev0", "scheduler": ["diffusers", "FlowMatchEulerDiscreteScheduler"], "text_encoder": ["transformers", "CLIPTextModelWithProjection"], "text_encoder_2": ["transformers", "CLIPTextModelWithProjection"], "text_encoder_3": ["transformers", "T5EncoderModel"], "tokenizer": ["transformers", "CLIPTokenizer"], "tokenizer_2": ["transformers", "CLIPTokenizer"], "tokenizer_3": ["transformers", "T5TokenizerFast"], "transformer": ["diffusers", "SD3Transformer2DModel"], "vae": ["diffusers", "AutoencoderKL"]}