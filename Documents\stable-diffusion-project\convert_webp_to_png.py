#!/usr/bin/env python3
"""
Convert WebP images to PNG format and delete original WebP files
"""
import os
import sys
from PIL import Image

def convert_webp_to_png(directory):
    """Convert all WebP files in directory to PNG and return lists of converted and original files"""
    converted_files = []
    webp_files = []
    
    if not os.path.exists(directory):
        print(f"Directory does not exist: {directory}")
        return converted_files, webp_files
    
    print(f"Processing directory: {directory}")
    
    for filename in os.listdir(directory):
        if filename.lower().endswith('.webp'):
            webp_path = os.path.join(directory, filename)
            png_filename = filename.rsplit('.', 1)[0] + '.png'
            png_path = os.path.join(directory, png_filename)
            
            try:
                print(f"Converting: {filename} -> {png_filename}")
                
                # Convert WebP to PNG
                with Image.open(webp_path) as img:
                    # Convert to RGB if necessary (WebP can have transparency)
                    if img.mode in ('RGBA', 'LA'):
                        # Create white background for transparent images
                        background = Image.new('RGB', img.size, (255, 255, 255))
                        if img.mode == 'RGBA':
                            background.paste(img, mask=img.split()[-1])
                        else:
                            background.paste(img)
                        img = background
                    elif img.mode != 'RGB':
                        img = img.convert('RGB')
                    
                    img.save(png_path, 'PNG', optimize=True)
                    converted_files.append(png_path)
                    webp_files.append(webp_path)
                    print(f"✓ Successfully converted: {filename}")
                    
            except Exception as e:
                print(f"✗ Error converting {filename}: {e}")
    
    return converted_files, webp_files

def main():
    # Directory path
    base_dir = r'C:\Users\<USER>\Documents\AI_OnlyFans\Lana_Pixie\final_output\7-23-2025'
    
    print("=== WebP to PNG Converter ===")
    print(f"Target directory: {base_dir}")
    
    # Convert main directory
    print("\n1. Converting main directory...")
    main_converted, main_webp = convert_webp_to_png(base_dir)
    
    # Convert Extra subdirectory
    extra_dir = os.path.join(base_dir, 'Extra')
    if os.path.exists(extra_dir):
        print("\n2. Converting Extra directory...")
        extra_converted, extra_webp = convert_webp_to_png(extra_dir)
        main_converted.extend(extra_converted)
        main_webp.extend(extra_webp)
    else:
        print("\n2. No Extra directory found, skipping...")
    
    print(f"\n=== CONVERSION SUMMARY ===")
    print(f"Total files converted: {len(main_converted)}")
    
    if main_converted:
        print("\nConverted files:")
        for png_file in main_converted:
            print(f"  ✓ {os.path.basename(png_file)}")
        
        # Ask user if they want to delete original WebP files
        print(f"\nOriginal WebP files to delete: {len(main_webp)}")
        for webp_file in main_webp:
            print(f"  - {os.path.basename(webp_file)}")
        
        response = input("\nDelete original WebP files? (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            deleted_count = 0
            for webp_file in main_webp:
                try:
                    os.remove(webp_file)
                    print(f"✓ Deleted: {os.path.basename(webp_file)}")
                    deleted_count += 1
                except Exception as e:
                    print(f"✗ Error deleting {os.path.basename(webp_file)}: {e}")
            
            print(f"\n=== CLEANUP SUMMARY ===")
            print(f"WebP files deleted: {deleted_count}/{len(main_webp)}")
        else:
            print("WebP files kept (not deleted)")
    else:
        print("No WebP files found to convert!")
    
    print("\n=== PROCESS COMPLETE ===")

if __name__ == "__main__":
    main()
