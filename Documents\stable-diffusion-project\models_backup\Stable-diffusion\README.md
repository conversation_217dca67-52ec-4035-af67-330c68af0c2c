# Required Model Files

The following model files need to be downloaded and placed in this directory:

1. `stable-diffusion-xl-base-1.0.safetensors` - SDXL base model
2. `sdxl-refiner-1.0.safetensors` - SDXL refiner model
3. `lana_pixie_dreambooth.safetensors` - Lana Pixie DreamBooth model

Once these files are in place, run the following command to generate checksums:

```powershell
Get-FileHash -Algorithm SHA256 stable-diffusion-xl-base-1.0.safetensors, sdxl-refiner-1.0.safetensors, lana_pixie_dreambooth.safetensors | Format-Table -Property Hash, Path -AutoSize | Out-File -FilePath checksum.txt
```

This will create a checksum.txt file that can be used to verify the integrity of the models in the future.
