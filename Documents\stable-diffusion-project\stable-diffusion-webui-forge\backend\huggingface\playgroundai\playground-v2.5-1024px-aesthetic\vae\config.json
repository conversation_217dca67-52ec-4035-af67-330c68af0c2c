{"_class_name": "AutoencoderKL", "_diffusers_version": "0.27.0.dev0", "act_fn": "silu", "block_out_channels": [128, 256, 512, 512], "down_block_types": ["DownEncoderBlock2D", "DownEncoderBlock2D", "DownEncoderBlock2D", "DownEncoderBlock2D"], "force_upcast": true, "in_channels": 3, "latent_channels": 4, "layers_per_block": 2, "norm_num_groups": 32, "out_channels": 3, "sample_size": 1024, "up_block_types": ["UpDecoderBlock2D", "UpDecoderBlock2D", "UpDecoderBlock2D", "UpDecoderBlock2D"], "latents_mean": [-1.6574, 1.886, -1.383, 2.5155], "latents_std": [8.4927, 5.9022, 6.5498, 5.2299], "scaling_factor": 0.5}