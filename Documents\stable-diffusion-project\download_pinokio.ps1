# Download Pinokio for Windows
Write-Host "=== Pinokio Download Script ===" -ForegroundColor Green
Write-Host ""

# Create download directory
$downloadPath = "C:\Downloads\Pinokio"
Write-Host "Creating download directory: $downloadPath" -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path $downloadPath | Out-Null
Set-Location $downloadPath

# Download URL
$url = "https://github.com/pinokiocomputer/pinokio/releases/download/2.18.8/Pinokio-2.18.8-win32-x64.exe"
$outputFile = "Pinokio-Setup.exe"

Write-Host "Downloading Pinokio from: $url" -ForegroundColor Yellow
Write-Host "Output file: $outputFile" -ForegroundColor Yellow
Write-Host ""

try {
    # Download with progress
    $webClient = New-Object System.Net.WebClient
    $webClient.DownloadFile($url, "$downloadPath\$outputFile")
    
    Write-Host "Download completed successfully!" -ForegroundColor Green
    
    # Check file size
    $fileInfo = Get-ChildItem $outputFile
    Write-Host "File size: $($fileInfo.Length) bytes" -ForegroundColor Green
    
    # Ask if user wants to install
    Write-Host ""
    $install = Read-Host "Do you want to install Pinokio now? (y/n)"
    
    if ($install -eq "y" -or $install -eq "Y") {
        Write-Host "Starting Pinokio installation..." -ForegroundColor Yellow
        Start-Process -FilePath ".\$outputFile" -Wait
        Write-Host "Installation completed!" -ForegroundColor Green
    } else {
        Write-Host "Installation skipped. Run $downloadPath\$outputFile to install later." -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "Download failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please try downloading manually from: https://pinokio.co" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Script completed!" -ForegroundColor Green
