@echo off
echo Installing Pinokio for Faceswap...
echo.

REM Create Pinokio directory
mkdir C:\pinokio 2>nul
cd /d C:\pinokio

echo Downloading Pinokio installer...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://github.com/pinokiocomputer/pinokio/releases/latest/download/Pinokio-Setup.exe' -OutFile 'pinokio-installer.exe' -UseBasicParsing}"

if exist pinokio-installer.exe (
    echo Running Pinokio installer...
    start /wait pinokio-installer.exe /S /D=C:\pinokio
    echo.
    echo Pinokio installation completed!
    echo.
    echo Starting Pinokio...
    if exist "C:\pinokio\Pinokio.exe" (
        start "" "C:\pinokio\Pinokio.exe"
        echo Pinokio is starting...
        echo Open your browser to http://localhost:42000
    ) else (
        echo Please run Pinokio from Start Menu or Desktop
    )
) else (
    echo Download failed. Please download manually from:
    echo https://pinokio.co
)

pause
